package com.qbook.insight.analyzer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.qbook.insight.analyzer.service.AnaResultService;
import com.qbook.insight.common.constant.ReportStage;
import com.qbook.insight.common.domain.report.result.BasicInfo;
import com.qbook.insight.common.domain.report.result.LegalPerson;
import com.qbook.insight.common.domain.report.result.Product;
import com.qbook.insight.common.domain.report.result.Result;
import com.qbook.insight.common.entity.IndicatorConfig;
import com.qbook.insight.common.entity.Report;
import com.qbook.insight.common.entity.ReportResult;
import com.qbook.insight.common.mapper.CorpMapper;
import com.qbook.insight.common.mapper.IndicatorConfigMapper;
import com.qbook.insight.common.mapper.ReportMapper;
import com.qbook.insight.common.mapper.ReportResultMapper;
import com.qbook.insight.common.util.IndicatorCalculator;
import com.qbook.insight.common.util.UniversalResultBuilder;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 报告结果分析实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AnaResultServiceImpl implements AnaResultService {

  @Resource private IndicatorConfigMapper indicatorConfigMapper;
  @Resource private IndicatorCalculator indicatorCalculator;
  @Resource private ReportResultMapper reportResultMapper;
  @Resource private CorpMapper corpMapper;
  @Resource private ReportMapper reportMapper;

  @Override
  @DSTransactional
  public void anaReportResult(Report report) {
    try {
      List<IndicatorConfig> configList = indicatorConfigMapper.selectConfigList();

      // 获取企业基本信息
      Map<String, Object> corpInfo = corpMapper.selectCorpInfoById(report.getCorpId());
      BasicInfo basicInfo = BeanUtil.toBean(corpInfo, BasicInfo.class);
      LegalPerson legalPerson = BeanUtil.toBean(corpInfo, LegalPerson.class);
      basicInfo.setLegalPerson(legalPerson);

      Map<String, Object> map = new HashMap<>();
      map.put("tax_id", basicInfo.getCreditCode());

      // 获取所有指标计算结果 - 税负分析
      Map<String, Object> resultMap =
          configList.stream()
              .map(config -> indicatorCalculator.calculate(config, map))
              .flatMap(m -> m.entrySet().stream())
              .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

      // TODO 模拟商品数据
      List<Map<String, Object>> products = new ArrayList<>();
      Map<String, Object> map1 = new HashMap<>();
      map1.put("amount",7318341);
      map1.put("quantity",1983);
      map1.put("avgPrice",3690.54);
      map1.put("unit","吨");
      map1.put("percentage",25.58);
      map1.put("name","黑色金属冶炼压延品+开平板");
      products.add(map1);

      resultMap.put("productAnalysis.mainPurchaseProducts", products);

      List<Map<String, Object>> InvoiceSalesAnalysis = new ArrayList<>();
      Map<String, Object> map2 = new HashMap<>();
      map2.put("year",2023);
      map2.put("corpName","浙江优钢智慧供应链有限公司");
      map2.put("status","开业");
      InvoiceSalesAnalysis.add(map2);
      map2 = new HashMap<>();
      map2.put("year",2022);
      map2.put("corpName","诸暨市宝航汽车修理有限公司");
      map2.put("status","开业");
      InvoiceSalesAnalysis.add(map2);
      map2 = new HashMap<>();
      map2.put("year",2022);
      map2.put("corpName","诸暨市振锋机械有限公司");
      map2.put("status","开业");
      InvoiceSalesAnalysis.add(map2);

      resultMap.put("invoiceAnalysis.salesInvoice.salesInvoice", InvoiceSalesAnalysis);


      log.debug("指标计算结果:{}", JSONUtil.toJsonStr(resultMap));

      Result result = UniversalResultBuilder.buildResult(resultMap);
      result.setBasicInfo(basicInfo);

      // TODO 其他章节分析结果

      ReportResult reportResult =
          new ReportResult()
              .setReportId(report.getId())
              .setUserId(report.getUserId())
              .setResult(JSONUtil.toJsonStr(result));

      log.info("报告结果生成完成: {}", JSONUtil.toJsonStr(reportResult));

//      reportResultMapper.insert(reportResult);

      LambdaUpdateWrapper<Report> updateWrapper = new LambdaUpdateWrapper<>();
      updateWrapper.set(Report::getStage, ReportStage.ANALYZED).eq(Report::getId, report.getId());
      reportMapper.update(null, updateWrapper);

    } catch (Exception e) {
      log.error("分析报告结果失败:", e);
    }
  }
}

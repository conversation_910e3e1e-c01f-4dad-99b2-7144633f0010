---
logging:
  file:
    path: "/tmp/logs"
    name: "${spring.application.name}"
  level:
    root: "INFO"
    com.qbook: "DEBUG"
    org.springframework: "INFO"
spring:
  datasource:
    url: "**************************************************************************"
    username: "zjks"
    password: "CNzjks*^%&!"
    driver-class-name: "com.mysql.cj.jdbc.Driver"
    hikari:
      maximum-pool-size: 10
      minimum-idle: 3
      connection-timeout: 10000
      idle-timeout: 10000
  redis:
    host: "localhost"
    port: 6379
    password: null
    database: 0
    connect-timeout: "10s"
    timeout: "10s"
    client-name: "${spring.application.name}"
    jedis:
      pool:
        enabled: true
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: "5000ms"
apidoc:
  enable: true
  version: "1.0.0-SNAPSHOT"
  permit-all-uris:
  - "/swagger-ui.html"
  - "/swagger-resources/**"
  - "/webjars/**"
  - "/v2/api-docs"
jwt:
  header: "Authorization"
  secret: "529a3ebbc3f34f6a9d88803d208c6a24d381d46261b64032baaad3cf6006647d"
  expiration: "180m"
  refresh-early: "20m"
wx:
  app-id: "wx46c7720d46151c49"
  app-secret: "14a4d02d3f06bdd78b8a14fdb34a89a0"
  token: "f025794b7b0f4fb0ab0170db29aa7d1c"
  aes-key: "ljKK9Bo1pSfvWQbfHGmOMaarIJfSHKLms4aAV8kGgbH"
  encryption-mode: "plain"
  qr-redis-prefix: "wx:qr:"
  qr-redis-expire: 180
  get-access-token-url: "https://api.weixin.qq.com/cgi-bin/token"
  get-ticket-url: "https://api.weixin.qq.com/cgi-bin/qrcode/create"
  get-qr-code-url: "https://mp.weixin.qq.com/cgi-bin/showqrcode"
  get-user-info-url: "https://api.weixin.qq.com/cgi-bin/user/info"
  get-oauth2-url: "https://open.weixin.qq.com/connect/oauth2/authorize"
  get-openid-url: "https://api.weixin.qq.com/sns/oauth2/access_token"
  pay:
    mch-id: ""
    mch-cert: ""
    mch-serial-no: ""
    mch-pri-key-path: ""
    cert-serial-no: ""
    cert-path: ""
    public-key-id: ""
    public-key-path: ""
    notify-url: ""
    api-v3-key: ""
    jsapi-pre-order-url: "https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi"
xlb:
  access-id: "e@712B04cbfa3p"
  access-token: "FvpPvdHyq1Hkmd6wgfTShUuF84AzZifSB4VyvZI800QSqCejW6O8u7EMddLGpN4L"
  corp-credit-code: "https://openapi.xiaolanben.com/api/4070"
  corp-base-info: "https://openapi.xiaolanben.com/api/1001"

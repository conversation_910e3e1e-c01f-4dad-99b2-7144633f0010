package com.qbook.insight.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.qbook.insight.common.constant.DsName;
import com.qbook.insight.common.domain.report.result.InvoiceAnalysis;
import com.qbook.insight.common.domain.report.result.InvoiceRiskStats;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 发票分析Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InvoiceAnalysisMapper {

  /** 获取发票统计数据 */
  @DS(DsName.DATA)
  InvoiceAnalysis getInvoiceAnalysis(
      @Param("taxId") String taxId,
      @Param("beginTime") Long beginTime,
      @Param("endTime") Long endTime);

  /** 上游企业统计 */
  @DS(DsName.DATA)
  List<InvoiceRiskStats> upstreamStats(
      @Param("taxId") String taxId,
      @Param("beginTime") Long beginTime,
      @Param("endTime") Long endTime);

  /** 下游企业统计 */
  @DS(DsName.DATA)
  List<InvoiceRiskStats> downstreamStats(
      @Param("taxId") String taxId,
      @Param("beginTime") Long beginTime,
      @Param("endTime") Long endTime);
}

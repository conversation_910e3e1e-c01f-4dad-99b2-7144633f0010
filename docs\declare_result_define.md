# 申报结果 JSON 字段结构定义

## 1. 《中华人民共和国企业所得税年度纳税申报表（A类 , 2017年版）》

### 

| 实体类中文名                     | 实体类英文名                    | 字段中文名                             | 字段英文名（驼峰命名）       |
| -------------------------------- | ------------------------------- | -------------------------------------- | ---------------------------- |
| 企业基础信息表                   | `CompanyInfo`                   | 所属国民经济行业                       | `industryCategory`           |
| 企业所得税年度纳税申报主表       | `CorporateIncomeTaxMain`        | 营业收入                               | `businessIncome`             |
| 企业所得税年度纳税申报主表       | `CorporateIncomeTaxMain`        | 实际应纳所得税额                       | `actualTaxPayable`           |
| 一般企业收入明细表               | `GeneralIncomeDetail`           | 出租固定资产收入金额                   | `leaseIncome`                |
| 金融企业收入明细表               | `FinanceIncomeDetail`           | 本年符合不征税收入条件的财政性资金金额 | `taxFreeGovFund`             |
| 纳税调整项目明细表               | `TaxAdjustmentDetail`           | 资产类调整项目调增金额                 | `assetAdjAddAmt`             |
| 纳税调整项目明细表               | `TaxAdjustmentDetail`           | 其他调增金额                           | `otherAdjAddAmt`             |
| 纳税调整项目明细表               | `TaxAdjustmentDetail`           | 罚金、罚款和被没收财物的损失调增金额   | `penaltyAdjAddAmt`           |
| 纳税调整项目明细表               | `TaxAdjustmentDetail`           | 税收滞纳金、加收利息调增金额           | `lateFeeAdjAddAmt`           |
| 纳税调整项目明细表               | `TaxAdjustmentDetail`           | 视同销售收入                           | `deemedSalesIncome`          |
| 纳税调整项目明细表               | `TaxAdjustmentDetail`           | 未按权责发生制原则确认的收入           | `nonAccrualIncome`           |
| 未按权责发生制确认收入调整表     | `NonAccrualAdjustment`          | 其他未按权责发生制确认收入纳税调整金额 | `otherNonAccrualAdj`         |
| 专项用途财政性资金纳税调整明细表 | `SpecialFiscalFundAdjustDetail` | 本年符合不征税收入条件的财政性资金金额 | `nonTaxableFiscalFundAmount` |
| 职工薪酬支出及纳税调整明细表     | `StaffCostAdjustment`           | 各类基本社会保障性缴款纳税调整金额     | `socialSecAdj`               |
| 捐赠支出及纳税调整明细表         | `DonationAdjustment`            | 限额扣除的公益性捐赠本年纳税调增金额   | `donationLimitAdj`           |
| 研发费用加计扣除优惠明细表       | `ResearchDeduction`             | 本年研发费用加计扣除总金额             | `rAndDTaxDeductAmt`          |



```json
{
  "companyInfo": {  // 企业基础信息表
    "industryCategory": "金属及金属矿批发" // 105所属国民经济行业
  },
  "researchDeduction": { // 研发费用加计扣除优惠明细表
    "rAndDTaxDeductAmt": 0 // 本年研发费用加计扣除总金额
  },
  "donationAdjustment": { // 捐赠支出及纳税调整明细表
    "donationLimitAdj": 0 // 限额扣除的公益性捐赠本年纳税调增金额
  },
  "financeIncomeDetail": { // 金融企业收入明细表
    "taxFreeGovFund": 0 // 本年符合不征税收入条件的财政性资金金额
  },
  "generalIncomeDetail": { // 一般企业收入明细表
    "leaseIncome": 0 // 出租固定资产收入金额
  },
  "staffCostAdjustment": { // 职工薪酬支出及纳税调整明细表
    "socialSecAdj": 0 // 各类基本社会保障性缴款纳税调整金额
  },
  "taxAdjustmentDetail": { // 纳税调整项目明细表
    "assetAdjAddAmt": 0, // 资产类调整项目调增金额
    "otherAdjAddAmt": 0, // 其他调增金额
    "lateFeeAdjAddAmt": 0, // 税收滞纳金、加收利息调增金额
    "nonAccrualIncome": 0, // 未按权责发生制原则确认的收入
    "penaltyAdjAddAmt": 0, // 罚金、罚款和被没收财物的损失调增金额
    "deemedSalesIncome": 0 // 视同销售收入
  },
  "nonAccrualAdjustment": { // 未按权责发生制确认收入调整表
    "otherNonAccrualAdj": 0 // 其他未按权责发生制确认收入纳税调整金额
  },
  "corporateIncomeTaxMain": { // 企业所得税年度纳税申报主表
    "businessIncome": 27004103.9, // 营业收入
    "actualTaxPayable": 20810 // 实际应纳所得税额
  }
}
```

## 2. 《中华人民共和国企业所得税月（季）度预缴纳税申报表（A类，2021年版）》

### 

| 实体类中文名                   | 实体类英文名             | 字段中文名   | 字段英文名（驼峰命名） |
| ------------------------------ | ------------------------ | ------------ | ---------------------- |
| 所得税月（季）度预缴纳税申报表 | `PrepaidIncomeTaxReturn` | 营业收入     | `businessIncome`       |
| 所得税月（季）度预缴纳税申报表 | `PrepaidIncomeTaxReturn` | 应纳所得税额 | `taxPayable`           |



```json
{
  "taxPayable": 26366.58, // 应纳所得税额
  "businessIncome": 7533308.23 // 营业收入
}
```

## 3. 《印花税纳税申报（报告）表》

```
[
  {
    "tax_item": "营业账簿",
    "detail_list": [
      {
        "name": "A",
        "amount": 10
      }
    ],
    "tax_payable": 100
  }
]
```

## 4. 《增值税及附加税费申报表（一般纳税人适用）》

```json
{
}
```

## 5. 《增值税及附加税费申报表（小规模纳税人适用）》

```json
{
}
```

## 6. 《社会保险费缴费申报表》

```json
[
  {
    "rate": 0.16, // 费率
    "feeType": "企业职工基本养老保险费", // 费种
    "levyItem": "职工基本养老保险", // 征收品目
    "levySubItem": "企业缴纳", // 征收子目
    "paymentBase": 34610, // 缴费基数
    "payableAmount": 5537.6 // 应缴费额
  }
]
```
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.qbook.insight.common.mapper.InvoiceAnalysisMapper">

  <select id="getInvoiceAnalysis"
    resultType="com.qbook.insight.common.domain.report.result.InvoiceAnalysis">
    SELECT
    purchaseTotalAmount,
    salesTotalAmount,
    (purchaseTotalAmount > salesTotalAmount) AS isPurchaseGTSalesTotal,
    purchaseTax,
    salesTax,
    (purchaseTax / salesTax &lt; 0.2) AS isSalesMLTPurchaseTax,
    (purchaseTotalAmount / salesTotalAmount &lt; 0.1) as isOnlySales
    FROM (
    SELECT
    (SELECT SUM(jshj) FROM invoice_purchase) AS purchaseTotalAmount,
    (SELECT SUM(jshj) FROM invoice_sales) AS salesTotalAmount,
    (SELECT SUM(hjse) FROM invoice_purchase) AS purchaseTax,
    (SELECT SUM(hjse) FROM invoice_sales) AS salesTax
    ) AS t;
  </select>

  <select id="upstreamStats"
    resultType="com.qbook.insight.common.domain.report.result.InvoiceRiskStats">
    SELECT gmfmc AS corpName, gmfnsrsbh AS taxId, SUM(jshj) AS totalAmount
    FROM invoice_purchase
    WHERE tax_id = #{taxId} AND fpztdm = '正常'
    GROUP BY corpName, taxId
    ORDER BY totalAmount DESC
    LIMIT 15
  </select>

  <select id="downstreamStats"
    resultType="com.qbook.insight.common.domain.report.result.InvoiceRiskStats">
    SELECT gmfmc AS corpName, gmfnsrsbh AS taxId, SUM(jshj) AS totalAmount
    FROM invoice_sales
    WHERE tax_id = #{taxId} AND fpztdm = '正常'
    GROUP BY corpName, taxId
    ORDER BY totalAmount DESC
    LIMIT 15
  </select>
</mapper>

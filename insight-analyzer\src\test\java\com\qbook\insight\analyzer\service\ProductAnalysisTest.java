package com.qbook.insight.analyzer.service;

import cn.hutool.json.JSONUtil;
import com.qbook.insight.common.domain.report.result.Result;
import com.qbook.insight.common.util.UniversalResultBuilder;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商品分析测试
 */
@SpringBootTest
@Slf4j
public class ProductAnalysisTest {

    @Test
    void testProductAnalysisDataMapping() {
        // 模拟商品数据
        List<Map<String, Object>> products = new ArrayList<>();
        Map<String, Object> product1 = new HashMap<>();
        product1.put("amount", 7318341);
        product1.put("quantity", 1983);
        product1.put("avgPrice", 3690.54);
        product1.put("unit", "吨");
        product1.put("percentage", 25.58);
        product1.put("name", "黑色金属冶炼压延品+开平板");
        products.add(product1);

        Map<String, Object> product2 = new HashMap<>();
        product2.put("amount", 5125000);
        product2.put("quantity", 1250);
        product2.put("avgPrice", 4100.00);
        product2.put("unit", "吨");
        product2.put("percentage", 17.89);
        product2.put("name", "热镀锌卷板");
        products.add(product2);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("productAnalysis.mainPurchaseProducts", products);

        log.info("输入数据: {}", JSONUtil.toJsonStr(resultMap));

        // 使用UniversalResultBuilder构建Result对象
        Result result = UniversalResultBuilder.buildResult(resultMap);

        log.info("构建结果: {}", JSONUtil.toJsonStr(result));

        // 验证商品数据是否正确映射
        assert result.getProductAnalysis() != null : "ProductAnalysis不应为null";
        assert result.getProductAnalysis().getMainPurchaseProducts() != null : "MainPurchaseProducts不应为null";
        assert result.getProductAnalysis().getMainPurchaseProducts().size() == 2 : "应该有2个商品";
        
        // 验证第一个商品的数据
        var firstProduct = result.getProductAnalysis().getMainPurchaseProducts().get(0);
        assert "黑色金属冶炼压延品+开平板".equals(firstProduct.getName()) : "商品名称不匹配";
        assert "吨".equals(firstProduct.getUnit()) : "单位不匹配";
        assert firstProduct.getQuantity().equals(1983f) : "数量不匹配";
        assert firstProduct.getAmount().intValue() == 7318341 : "金额不匹配";
        assert firstProduct.getAvgPrice().equals(3690.54) : "均价不匹配";
        assert firstProduct.getPercentage().equals(25.58) : "占比不匹配";

        log.info("商品分析数据映射测试通过！");
    }
}
